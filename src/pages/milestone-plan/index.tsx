import { useRef } from 'react';

import type { ActionType, FormInstance, ProColumns } from '@ant-design/pro-components';
import { CustomUpload } from '@/common/common-view';
import { message, Space } from 'antd';
import { AuthButton, ModalBtn } from '@hbwow/components';
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import UsageTips from '@/components/UsageTips';

import { plmApis } from '@/api';
import { useDictMap, useDownload, useSystemMenu } from '@/api/querys/common';
import { getDataFromLocalStorage } from '@/utils';
import { ProTable } from '@/components/ProTable';

const MilestonePlan = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const { data: dictMap } = useDictMap();
  const { mutate: mutateDownload, isPending: isPendingDownload } = useDownload();
  const { data: dataSystemMenu } = useSystemMenu();

  const columns: ProColumns[] = [
    {
      title: '项目',
      dataIndex: 'projectCode',
      hideInTable: true,
      order: 999,
      fieldProps: {
        placeholder: '按项目名称或编号模糊查询',
      },
    },
    {
      title: '里程碑编号',
      dataIndex: 'milestoneCode',
      fixed: 'left',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '里程碑名称',
      dataIndex: 'milestoneName',
      fixed: 'left',
      hideInSearch: true,
    },
    {
      title: '项目编号',
      dataIndex: 'projectCode',
      order: 100,
      hideInSearch: true,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 200,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '所属交付组',
      dataIndex: 'deliveryTeam',
      valueEnum: () => dictMap.getLabelByValue['plm_deliver_teams'],
    },
    {
      title: '所属经营单元',
      dataIndex: 'managementUnit',
      valueEnum: () => dictMap.getLabelByValue['plm_teams'],
    },
    {
      title: '里程碑开始日期',
      dataIndex: 'startDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '里程碑结束日期',
      dataIndex: 'endDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '计划人工工量（人日）',
      dataIndex: 'planWorkLoad',
      hideInSearch: true,
    },
    {
      title: '工作日',
      dataIndex: 'workDay',
      hideInSearch: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 200,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 100,
      render: (_, record) => {
        const { id } = record;

        return (
          <Space>
            <AuthButton
              isBtn={false}
              authId='milestonePlan_delete'
              authList={dataSystemMenu?.perms}
            >
              <ModalBtn
                buttonProps={{
                  className: 'px-0',
                }}
                onOk={async () => {
                  await plmApis.plmMilestoneDeleteCreate(id);
                  actionRef.current?.reload();
                }}
              >
                删除
              </ModalBtn>
            </AuthButton>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable
        storeKey='/projectPlan/milestonePlan'
        scroll={{ x: 2000 }}
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        pageRequest={plmApis.plmMilestonePageCreate}
        headerTitle={
          <div>
            <>里程碑计划</>
            <UsageTips
              buttonProps={{
                className: 'ml-4',
              }}
            />
          </div>
        }
        toolBarRender={() => [
          <AuthButton
            key='1'
            authId='milestonePlan_upload_template'
            authList={dataSystemMenu?.perms}
            isBtn={false}
          >
            <CustomUpload
              btnName='导入里程碑计划'
              buttonProps={{
                icon: <UploadOutlined />,
              }}
              downloadInfo={{
                reqUrl: '/pmt/plm/milestone/template',
                method: 'POST',
              }}
              url='/pmt/plm/milestone/importData'
              callback={() => actionRef.current?.reload()}
            />
          </AuthButton>,
          <AuthButton
            key='2'
            authId='milestonePlan_export'
            authList={dataSystemMenu?.perms}
            icon={<DownloadOutlined />}
            loading={isPendingDownload}
            onClick={() => {
              mutateDownload(
                {
                  reqUrl: '/pmt/plm/milestone/export',
                  method: 'POST',
                  token: `Bearer ${getDataFromLocalStorage('authorization')}`,
                  data: {
                    ...(formRef.current?.getFieldsValue() || {}),
                  },
                },
                {
                  onError(err) {
                    message.error('导出失败：' + err.message);
                  },
                },
              );
            }}
          >
            导出
          </AuthButton>,
        ]}
      />
    </>
  );
};

export default MilestonePlan;
