import { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { CommonProTable, CustomUpload } from '@/common/common-view';
import { Button, Space } from 'antd';
import { ModalBtn, AuthButton } from '@hbwow/components';
import { UploadOutlined } from '@ant-design/icons';
import UsageTips from '@/components/UsageTips';
import CU from './CU';

import { plmApis } from '@/api';
import { useDictMap, useSystemMenu } from '@/api/querys/common';
import { formatSearch } from '@/utils/common';
import { ProTable } from '@/components/ProTable';

const ProjectOverview = () => {
  const actionRef = useRef<ActionType>();
  const { data: dictMap } = useDictMap();
  const { data: dataSystemMenu } = useSystemMenu();

  const navigate = useNavigate();

  const defaultCUInfo = {
    open: false,
    id: '',
  };
  const [CUInfo, setCUInfo] = useState({ ...defaultCUInfo });

  const columns: ProColumns[] = [
    {
      title: '项目',
      dataIndex: 'projectCode',
      hideInTable: true,
      order: 999,
      fieldProps: {
        placeholder: '按项目名称或编号模糊查询',
      },
    },
    {
      title: '项目简称',
      dataIndex: 'projectAlias',
      fixed: 'left',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '项目编号',
      dataIndex: 'projectCode',
      order: 100,
      hideInSearch: true,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 200,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '客户单位',
      dataIndex: 'customerName',
      hideInSearch: true,
    },
    {
      title: '项目性质',
      dataIndex: 'projectNature',
      hideInSearch: true,
      valueEnum: () => dictMap.getLabelByValue['plm_project_nature'],
    },
    {
      title: '所属交付组',
      dataIndex: 'deliveryTeam',
      valueEnum: () => dictMap.getLabelByValue['plm_deliver_teams'],
    },
    {
      title: '所属经营单元',
      dataIndex: 'managementUnit',
      valueEnum: () => dictMap.getLabelByValue['plm_teams'],
    },
    {
      title: '优先级',
      dataIndex: 'priorityLevel',
      valueEnum: () => dictMap.getLabelByValue['plm_priority'],
    },
    {
      title: '项目经理',
      dataIndex: 'projectManager',
      hideInSearch: true,
    },
    {
      title: '项目阶段',
      dataIndex: 'projectStage',
      valueEnum: () => dictMap.getLabelByValue['plm_project_stage'],
    },
    {
      title: '内部立项日期',
      dataIndex: 'approvalDate',
      hideInSearch: true,
      valueType: 'date',
    },
    {
      title: '计划启动时间',
      dataIndex: 'planStartDate',
      hideInSearch: true,
      valueType: 'date',
    },
    {
      title: '计划验收日期',
      dataIndex: 'planEndDate',
      hideInSearch: true,
      valueType: 'date',
    },
    {
      title: '计划结项时间',
      dataIndex: 'planClosingDate',
      hideInSearch: true,
      valueType: 'date',
    },
    {
      title: '是否预算内项目',
      dataIndex: 'budgetProject',
      hideInSearch: true,
      valueEnum: () => dictMap.getLabelByValue['0-1'],
    },
    {
      title: '税前收入（万元）',
      dataIndex: 'pretaxIncome',
      hideInSearch: true,
    },
    {
      title: '税后收入（万元）',
      dataIndex: 'revenue',
      hideInSearch: true,
    },
    {
      title: '预计总人工成本（万元）',
      dataIndex: 'planCostTotal',
      hideInSearch: true,
    },
    {
      title: '年度预计人工成本（万元）',
      dataIndex: 'yearPlanCost',
      hideInSearch: true,
    },
    {
      title: '项目状态',
      dataIndex: 'projectState',
      valueEnum: () => dictMap.getLabelByValue['plm_project_state'],
    },
    {
      title: '操作',
      fixed: 'right',
      width: 220,
      hideInSearch: true,
      render: (_, record) => {
        const { id, projectCode } = record;

        return (
          <Space>
            <Button
              key='monthly-plan'
              type='link'
              className='px-0'
              onClick={() => {
                navigate({
                  pathname: '/projectPlan/monthlyPlan',
                  search: formatSearch({ projectCode: projectCode }),
                });
              }}
            >
              月度计划
            </Button>
            <Button
              key='monthly-progress'
              type='link'
              className='px-0'
              onClick={() => {
                navigate({
                  pathname: '/projectProgress/monthlyProgress',
                  search: formatSearch({ projectCode: projectCode }),
                });
              }}
            >
              月度进展
            </Button>
            <AuthButton
              key='edit'
              authId='overflow_update'
              authList={dataSystemMenu?.perms}
              type='link'
              className='px-0'
              onClick={() => {
                setCUInfo({
                  open: true,
                  id,
                });
              }}
            >
              编辑
            </AuthButton>
            <AuthButton
              key='delete'
              isBtn={false}
              authId='overflow_delete'
              authList={dataSystemMenu?.perms}
            >
              <ModalBtn
                buttonProps={{
                  className: 'px-0',
                }}
                onOk={async () => {
                  await plmApis.plmPiDeleteCreate(id);
                  actionRef.current?.reload();
                }}
              >
                删除
              </ModalBtn>
            </AuthButton>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable
        storeKey='/projectManager/overview'
        scroll={{ x: 4000 }}
        columns={columns}
        actionRef={actionRef}
        pageRequest={plmApis.plmPiPageCreate}
        headerTitle={
          <div>
            <div>项目总览</div>
            <UsageTips
              buttonProps={{
                className: 'ml-4',
              }}
            />
          </div>
        }
        toolBarRender={() => [
          <AuthButton
            key='1'
            authId='overflow_upload_template'
            authList={dataSystemMenu?.perms}
            isBtn={false}
          >
            <CustomUpload
              btnName='导入项目总览'
              buttonProps={{
                icon: <UploadOutlined />,
              }}
              downloadInfo={{
                reqUrl: '/pmt/plm/pi/template',
                method: 'POST',
              }}
              url='/pmt/plm/pi/importData'
              callback={() => actionRef.current?.reload()}
            />
          </AuthButton>,
          <AuthButton
            key='2'
            authId='milestonePlan_upload_template'
            authList={dataSystemMenu?.perms}
            isBtn={false}
          >
            <CustomUpload
              btnName='导入里程碑计划'
              buttonProps={{
                icon: <UploadOutlined />,
              }}
              downloadInfo={{
                reqUrl: '/pmt/plm/milestone/template',
                method: 'POST',
              }}
              url='/pmt/plm/milestone/importData'
              callback={() => {
                actionRef.current?.reload();

                navigate({
                  pathname: '/projectPlan/milestonePlan',
                });
              }}
            />
          </AuthButton>,
        ]}
      />

      <CU
        id={CUInfo.id}
        onOkCallback={() => {
          setCUInfo({ ...defaultCUInfo });
          actionRef.current?.reload();
        }}
        modalProps={{
          open: CUInfo.open,
          onCancel: () => {
            setCUInfo({ ...defaultCUInfo });
          },
        }}
      />
    </>
  );
};

export default ProjectOverview;
