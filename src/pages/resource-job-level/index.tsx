import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { useRef } from 'react';
import './index.less';
import { plmbaseApis } from '@/api';
import { CustomUpload } from '@/common/common-view';
import { useDictMap } from '@/api/querys/common';
import { ProTable } from '@/components/ProTable';

const ResourceJobLevel = () => {
  const actionRef = useRef<ActionType>();
  const { data: dictMap } = useDictMap();

  const columns: ProColumns[] = [
    {
      title: '岗位',
      dataIndex: 'jobName',
    },
    {
      title: '岗级',
      dataIndex: 'jobLevelId',
      valueEnum: () => dictMap.getLabelByValue['plm_jobs'],
    },
    {
      title: '人数',
      dataIndex: 'staffNumber',
      hideInSearch: true,
    },
  ];

  return (
    <>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        pageRequest={(para: any) => plmbaseApis.pageUsingPost9(para)}
        headerTitle='岗级总览'
        toolBarRender={() => [
          <CustomUpload
            key='1'
            btnName='导入岗级总览'
            url='/pmt/staff/job_level/importData'
            callback={() => actionRef.current?.reload()}
          />,
        ]}
      />
    </>
  );
};

export default ResourceJobLevel;
