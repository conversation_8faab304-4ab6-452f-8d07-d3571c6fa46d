import { useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import type { ActionType, FormInstance, ProColumns } from '@ant-design/pro-components';
import UsageTips from '@/components/UsageTips';

import { plmApis } from '@/api';
import { useDictMap } from '@/api/querys/common';
import { useSP } from '@/utils/common';
import { ProTable } from '@/components/ProTable';

const ProjectMonthlyPlan = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const navigate = useNavigate();
  const location = useLocation();
  const { projectCode } = useSP();
  const { data: dictMap } = useDictMap();

  // 带参跳转进来
  useEffect(() => {
    if (projectCode) {
      formRef.current?.setFieldsValue({
        projectCode,
      });
      formRef.current?.submit();

      // 清空路由状态
      navigate(location.pathname, { replace: true });
    }
  }, [projectCode]);

  const columns: ProColumns[] = [
    {
      title: '项目',
      dataIndex: 'projectCode',
      hideInTable: true,
      order: 999,
      fieldProps: {
        placeholder: '按项目名称或编号模糊查询',
      },
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 200,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '项目编号',
      dataIndex: 'projectCode',
      hideInSearch: true,
    },
    {
      title: '里程碑编号',
      dataIndex: 'planCode',
      hideInSearch: true,
    },
    {
      title: '统计截至月份',
      dataIndex: 'planMonth',
      valueType: 'dateMonth',
      fieldProps: {
        format: 'YYYY-MM',
      },
    },
    {
      title: '所属交付组',
      dataIndex: 'deliveryTeam',
      valueEnum: () => dictMap.getLabelByValue['plm_deliver_teams'],
    },
    {
      title: '所属经营单元',
      dataIndex: 'managementUnit',
      valueEnum: () => dictMap.getLabelByValue['plm_teams'],
    },
    {
      title: '统计截至日期',
      dataIndex: 'endDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '本月工作日',
      dataIndex: 'workDay',
      hideInSearch: true,
    },
    {
      title: '当月PV（人日）',
      dataIndex: 'planMonthWorkLoad',
      hideInSearch: true,
    },
    {
      title: '当月累计PV（人日）',
      dataIndex: 'planWorkLoad',
      hideInSearch: true,
    },
  ];

  return (
    <>
      <ProTable
        storeKey='projectPlan/monthlyPlan'
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        manualRequest={Boolean(projectCode)}
        pageRequest={plmApis.plmPlanPageCreate}
        headerTitle={
          <div>
            <>项目月度计划</>
            <UsageTips
              buttonProps={{
                className: 'ml-4',
              }}
            />
          </div>
        }
        toolBarRender={() => []}
      />
    </>
  );
};

export default ProjectMonthlyPlan;
