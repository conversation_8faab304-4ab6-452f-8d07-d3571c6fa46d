import { useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import type { ActionType, FormInstance, ProColumns } from '@ant-design/pro-components';
import { message } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import UsageTips from '@/components/UsageTips';

import { plmApis } from '@/api';
import { useDictMap, useDownload, useSystemMenu } from '@/api/querys/common';
import { useSP } from '@/utils/common';
import { getDataFromLocalStorage } from '@/utils';
import { AuthButton } from '@hbwow/components';
import { ProTable } from '@/components/ProTable';

const ProjectMonthlyProgress = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const navigate = useNavigate();
  const location = useLocation();
  const { projectCode } = useSP();
  const { data: dictMap } = useDictMap();
  const { mutate: mutateDownload, isPending: isPendingDownload } = useDownload();
  const { data: dataSystemMenu } = useSystemMenu();

  // 带参跳转进来
  useEffect(() => {
    if (projectCode) {
      formRef.current?.setFieldsValue({
        projectCode,
      });
      formRef.current?.submit();

      // 清空路由状态
      navigate(location.pathname, { replace: true });
    }
  }, [projectCode]);

  const columns: ProColumns[] = [
    {
      title: '项目',
      dataIndex: 'projectCode',
      hideInTable: true,
      order: 999,
      fieldProps: {
        placeholder: '按项目名称或编号模糊查询',
      },
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 200,
      fixed: 'left',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '项目编号',
      dataIndex: 'projectCode',
      hideInSearch: true,
    },
    {
      title: '所属交付组',
      dataIndex: 'deliveryTeam',
      valueEnum: () => dictMap.getLabelByValue['plm_deliver_teams'],
    },
    {
      title: '所属经营单元',
      dataIndex: 'managementUnit',
      valueEnum: () => dictMap.getLabelByValue['plm_teams'],
    },
    {
      title: '统计截至月份',
      dataIndex: 'statMonth',
      valueType: 'dateMonth',
      fieldProps: {
        format: 'YYYY-MM',
      },
    },
    {
      title: '统计截至日期',
      dataIndex: 'statDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '本月累计PV',
      dataIndex: 'planWorkLoad',
      hideInSearch: true,
    },
    {
      title: '本月累计EV',
      dataIndex: 'workLoadTotal',
      hideInSearch: true,
    },
    {
      title: '进度偏差率',
      dataIndex: 'paceOffset',
      hideInSearch: true,
      renderText(text) {
        if (text >= 0) {
          return <span>{text}%</span>;
        }
        if (text < 0) {
          return <span className='text-red-500'>{text}%</span>;
        }
        return '-';
      },
    },
    {
      title: '进度风险',
      dataIndex: 'paceRisk',
      hideInSearch: true,
      // valueEnum: () => dictMap.getLabelByValue['0-1'],
      renderText(text) {
        if (text === 0) {
          return <span>{dictMap.getLabelByValue['0-1'][text]}</span>;
        }
        if (text === 1) {
          return <span className='text-red-500'>{dictMap.getLabelByValue['0-1'][text]}</span>;
        }
        return '-';
      },
    },
  ];

  return (
    <>
      <ProTable
        storeKey='projectProgress/monthlyProgress'
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        manualRequest={Boolean(projectCode)}
        pageRequest={plmApis.plmPacePageCreate}
        headerTitle={
          <div>
            <>项目进展列表</>
            <UsageTips
              buttonProps={{
                className: 'ml-4',
              }}
            />
          </div>
        }
        toolBarRender={() => [
          <AuthButton
            authId='monthlyProgress_export'
            authList={dataSystemMenu?.perms}
            key='2'
            icon={<DownloadOutlined />}
            loading={isPendingDownload}
            onClick={() => {
              mutateDownload(
                {
                  reqUrl: '/pmt/plm/pace/export',
                  method: 'POST',
                  token: `Bearer ${getDataFromLocalStorage('authorization')}`,
                  data: {
                    ...(formRef.current?.getFieldsValue() || {}),
                  },
                },
                {
                  onError(err) {
                    message.error('导出失败：' + err.message);
                  },
                },
              );
            }}
          >
            导出
          </AuthButton>,
        ]}
      />
    </>
  );
};

export default ProjectMonthlyProgress;
