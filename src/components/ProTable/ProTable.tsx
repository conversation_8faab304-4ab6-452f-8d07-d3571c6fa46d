import { useRef } from 'react';
import {
  ProTable as AntdProTable,
  FormInstance,
  ParamsType,
  ProTableProps,
} from '@ant-design/pro-components';
import { useShallow } from 'zustand/shallow';
import useStore from './useStore';

// 扩展 ProTable 的 props 接口
export interface IProTableProps<
  DataType extends Record<string, unknown> = Record<string, unknown>,
  Params extends ParamsType = ParamsType,
  ValueType = 'text',
> extends ProTableProps<DataType, Params, ValueType> {
  /** 分页请求函数 */
  pageRequest?: (params: Record<string, unknown>) => Promise<{
    data?: {
      data?: DataType[];
      totalRecords?: number;
    };
  }>;
  /** 存储键，用于保存表格状态 */
  storeKey?: string;
}

const ProTable = <
  DataType extends Record<string, unknown> = Record<string, unknown>,
  Params extends ParamsType = ParamsType,
  ValueType = 'text',
>(
  props: IProTableProps<DataType, Params, ValueType>,
) => {
  const { formRef, storeKey = '', pageRequest, headerTitle, ...rest } = props;

  // 从 store 中获取表格参数
  const [curTableParams, updateTableParams] = useStore(
    useShallow((store) => [store.tableParams[storeKey], store.updateTableParams]),
  );

  // 创建表单引用
  const _formRef = useRef<FormInstance>();
  const curFormRef = formRef || _formRef;

  // 默认配置
  const defaultProps = {
    formRef: curFormRef,
    //  debounceTime:0,
    // 滚动配置
    scroll: { x: 'max-content' },
    // 可编辑配置
    editable: {
      type: 'multiple' as const,
    },
    // 行键
    rowKey: 'id',
    // 不显示设置按钮
    options: false,
    // 表单配置
    form: {
      initialValues: storeKey ? curTableParams : undefined,
    },
    // 重置处理
    onReset: () => {
      const resetData = Object.keys(curFormRef.current?.getFieldsValue() || {}).reduce(
        (acc, cur) => {
          acc[cur] = undefined;
          return acc;
        },
        {} as Record<string, unknown>,
      );

      curFormRef.current?.setFieldsValue(resetData);
      curFormRef.current?.submit();

      return;
    },
    // 请求处理
    request: async (params: Record<string, unknown>) => {
      if (!pageRequest) {
        return {
          data: [],
          success: true,
          total: 0,
        };
      }

      updateTableParams(storeKey, params);

      try {
        const response = await pageRequest({
          ...params,
          pageNumber: params.current,
          pageSize: params.pageSize,
        });

        return {
          data: response?.data?.data || [],
          success: !!response?.data?.data,
          total: response?.data?.totalRecords || 0,
        };
      } catch (error) {
        // 可以在这里添加错误上报逻辑
        return {
          data: [],
          success: false,
          total: 0,
        };
      }
    },
    // 分页配置
    pagination: {
      defaultPageSize: 20,
      // showQuickJumper: true,
      showSizeChanger: true,
    },
    // 日期格式化
    dateFormatter: 'yyyy-MM-dd' as const,
    // 搜索配置
    search: {
      labelWidth: 'auto' as const,
      defaultCollapsed: false,
    },
  };

  // 合并配置 - 使用简单的对象展开，避免深度合并 React 元素
  const proTableProps = { ...defaultProps, ...rest };

  return <AntdProTable {...proTableProps} />;
};

export default ProTable;
