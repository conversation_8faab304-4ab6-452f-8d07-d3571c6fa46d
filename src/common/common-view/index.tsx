import { useState } from 'react';

// import { getPageResult } from '@/api';
import { ParamsType, ProTable, ProTableProps } from '@ant-design/pro-components';
import { Button, ButtonProps, message, Modal, Steps, Upload, UploadProps } from 'antd';

import { getHeader } from '@/api/axiosConfig';
import { getDataFromLocalStorage } from '@/utils';
import { useDownload } from '@/api/querys/common';

type ParaType = {
  btnName: string;
  url: string;
  data: any;
  callback: (isSuccess: boolean) => void;
  buttonProps?: ButtonProps;
  downloadInfo?: {
    reqUrl: string;
    method?: string;
  };
};

export const CustomUpload: React.FC<any> = (props: ParaType) => {
  const { btnName, url, callback, buttonProps = {} } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { mutate: mutateDownload, isPending: isPendingDownload } = useDownload();

  const uploadProps: UploadProps = {
    // name: 'file',
    action: url,
    data: props.data,
    headers: {
      ...getHeader(),
    },
    showUploadList: false,
    onChange(info) {
      if (info.file.status !== 'uploading') {
        // console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done' || info.file.status === 'error') {
        if (info.file.response.code === 200) {
          setIsModalOpen(false);
          Modal.success({
            content: `导入数据成功！`,
            afterClose: () => callback?.(true),
          });
        } else {
          message.error(`文件   ${info.file.name}   导入失败:${info.file.response?.message}`);
        }
      }
    },
  };

  return (
    <>
      <Button type='primary' onClick={() => setIsModalOpen(true)} {...buttonProps}>
        {btnName}
      </Button>
      {isModalOpen && (
        <Modal
          title={btnName}
          open={isModalOpen}
          footer={null}
          onCancel={() => setIsModalOpen(false)}
        >
          <Steps
            direction='vertical'
            size='small'
            items={[
              {
                status: 'process',
                title: (
                  <Button
                    type='link'
                    className='p-0 h-auto'
                    loading={isPendingDownload}
                    onClick={() => {
                      if (props.downloadInfo) {
                        mutateDownload(
                          {
                            token: `Bearer ${getDataFromLocalStorage('authorization')}`,
                            ...props.downloadInfo,
                          },
                          {
                            onError: (err) => {
                              if (err.code !== 200) {
                                message.error(err.message || '未知错误');
                              }
                            },
                          },
                        );
                      }
                    }}
                  >
                    点击下载模板文件
                  </Button>
                ),
              },
              {
                status: 'process',
                title: '请按模板格式填写需要导入的信息',
              },
              {
                status: 'process',
                title: (
                  <Upload {...uploadProps}>
                    <Button type='link' className='p-0 h-auto'>
                      点击上传完善后的模板
                    </Button>
                  </Upload>
                ),
              },
            ]}
          />
        </Modal>
      )}
    </>
  );
};

export const CommonProTable = <
  DataType extends Record<string, any>,
  Params extends ParamsType = ParamsType,
  ValueType = 'text',
>(
  props: ProTableProps<DataType, Params, ValueType> & { pageRequest?: any },
) => {
  return (
    <>
      <ProTable
        // debounceTime={0}
        scroll={{ x: 'max-content' }}
        editable={{
          type: 'multiple',
        }}
        rowKey='id'
        options={false}
        request={async (params) => {
          // console.log(params);
          // return getPageResult(
          //   props.pageRequest({
          //     ...params,
          //     pageNumber: params.current,
          //     pageSize: params.pageSize,
          //   }),
          //   '获取数据失败',
          // );

          const r = await props.pageRequest?.({
            ...params,
            pageNumber: params.current,
            pageSize: params.pageSize,
          });

          return {
            data: r?.data?.data,
            success: !!r?.data?.data,
            total: r?.data?.totalRecords,
          };
        }}
        pagination={{
          defaultPageSize: 20,
          // showQuickJumper: true,
          showSizeChanger: true,
        }}
        dateFormatter='yyyy-MM-dd'
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
        }}
        {...props}
      />
    </>
  );
};
